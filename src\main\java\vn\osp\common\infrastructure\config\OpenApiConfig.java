package vn.osp.common.infrastructure.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.*;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;

@Configuration
@EnableConfigurationProperties(OpenApiProperties.class)
public class OpenApiConfig {
    private final OpenApiProperties properties;

    public OpenApiConfig(OpenApiProperties properties) {
        this.properties = properties;
    }

    @Bean
    public OpenAPI defaultOpenAPI() {
        Components components = new Components();
        OpenAPI openAPI = new OpenAPI()
                .info(new Info()
                        .title(properties.getTitle())
                        .version(properties.getVersion())
                        .description(properties.getDescription())
                );

        // Security items
        if (properties.isEnableBearer()) {
            components.addSecuritySchemes("bearerAuth",
                    new SecurityScheme()
                            .type(SecurityScheme.Type.HTTP)
                            .scheme("bearer")
                            .bearerFormat("JWT"));
            openAPI.addSecurityItem(new SecurityRequirement().addList("bearerAuth"));
        }

        if (properties.isEnableBasic()) {
            components.addSecuritySchemes("basicAuth",
                    new SecurityScheme()
                            .type(SecurityScheme.Type.HTTP)
                            .scheme("basic"));
            openAPI.addSecurityItem(new SecurityRequirement().addList("basicAuth"));
        }

        if (properties.isEnableApiKey()) {
            components.addSecuritySchemes("apiKeyAuth",
                    new SecurityScheme()
                            .type(SecurityScheme.Type.APIKEY)
                            .in(SecurityScheme.In.HEADER)
                            .name("X-API-KEY"));
            openAPI.addSecurityItem(new SecurityRequirement().addList("apiKeyAuth"));
        }

        if (properties.isEnableOAuth2()) {
            OpenApiProperties.OAuth2Properties oauth2 = properties.getOauth2();

            OAuthFlow flow = new OAuthFlow()
                    .authorizationUrl(oauth2.getAuthorizationUrl())
                    .tokenUrl(oauth2.getTokenUrl());

            Scopes scopes = new Scopes();
            if (oauth2.getScopes() != null) {
                oauth2.getScopes().forEach(scopes::addString);
            }
            flow.setScopes(scopes);

            OAuthFlows flows = new OAuthFlows();
            switch (oauth2.getFlow()) {
                case "clientCredentials":
                    flows.clientCredentials(flow);
                    break;
                case "password":
                    flows.password(flow);
                    break;
                default:
                    flows.authorizationCode(flow);
                    break;
            }

            components.addSecuritySchemes("oauth2Scheme",
                    new SecurityScheme()
                            .type(SecurityScheme.Type.OAUTH2)
                            .flows(flows));

            List<String> scopesList = oauth2.getScopes() != null ?
                    List.copyOf(oauth2.getScopes().keySet()) :
                    Collections.emptyList();

            openAPI.addSecurityItem(new SecurityRequirement().addList("oauth2Scheme",scopesList));

            // Swagger UI extensions để tự động điền clientId / clientSecret
            openAPI.addExtension("x-token-url", oauth2.getTokenUrl());
            if (oauth2.getClientId() != null) {
                openAPI.addExtension("x-client-id", oauth2.getClientId());
            }
            if (oauth2.getClientSecret() != null) {
                openAPI.addExtension("x-client-secret", oauth2.getClientSecret());
            }
        }

        openAPI.components(components);
        return openAPI;
    }
}
